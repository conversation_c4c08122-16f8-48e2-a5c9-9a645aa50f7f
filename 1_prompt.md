## Objective: Implement Measurable Habit Tracking

The goal is to implement the final part of our core tracking functionality: allowing users to log numerical values for "measurable" habits. The implementation should closely follow the workflow established in the reference project.

## Reference Workflow

The primary reference for this task is the existing implementation in the `uhabits-dev` project. The developer must first study how the `HabitListView.kt`, `ListHabitsBehavior.kt`, and the associated dialogs work together to handle clicks on measurable habits.

## Implementation Plan

### 1. Differentiate Click Behavior Based on Habit Type

* **File to Modify:** The file that contains the click listener logic for the habit completion circles (likely `HabitListView.kt` or a similar view-related file).
* **Action:**
    * Inside the `onClickListener` for the checkmark circles, add conditional logic to check the `type` of the habit that was clicked.
    * **If the habit is "Yes/No,"** the existing logic to toggle completion should run.
    * **If the habit is "Measurable,"** the app should not toggle the checkmark directly. Instead, it must trigger the display of a numerical input dialog.

### 2. Create and Display the Numerical Input Dialog

* **Goal:** When a measurable habit is tapped, a dialog must appear asking the user to input a number.
* **Action:**
    * Create a new `AlertDialog` or a custom dialog fragment for this purpose. It should contain a text field that only accepts numerical input and "OK" and "Cancel" buttons.
    * This dialog should be launched from the view layer (`HabitListView.kt` or its controlling Activity/Fragment) when the condition from Step 1 is met.

### 3. Implement the ViewModel Logic to Save the Value

* **File to Modify:** The main `ViewModel` for the home screen (`MainViewModel.kt` or equivalent).
* **Action:**
    1.  Create a new function in the ViewModel, such as `saveMeasurableHabitCompletion(habitId: Long, date: LocalDate, value: Int)`.
    2.  When the user clicks "OK" in the numerical input dialog, the dialog should call this new ViewModel function, passing the habit's ID, the selected date, and the number entered by the user.
    3.  This function will then call the appropriate repository method to either **create a new `Completion` record or update an existing one** for that habit and date, saving the number in the `value` field of the record.

### 4. Verification

After implementation, the following scenarios must be tested:

* **Yes/No Habits:** Confirm that tapping a Yes/No habit still toggles its completion instantly as before.
* **Measurable Habits:**
    * Confirm that tapping a measurable habit opens the numerical input dialog.
    * Enter a number and press "OK." Confirm that the data is saved and the UI updates to reflect the completion (e.g., the circle fills in).
    * Tap the same circle again, update the number, and confirm the record is updated correctly.
    * Tap a circle and press "Cancel." Confirm that no changes are made.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation. 
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.