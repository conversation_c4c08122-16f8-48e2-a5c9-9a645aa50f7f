FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:packageDebug'.
> java.io.IOException: Unable to delete directory 'E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\packageDebug\tmp'
    Failed to delete some children. This might happen because a process has files open or has its working directory set in the target directory.
    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\packageDebug\tmp\debug\zip-cache
    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\packageDebug\tmp\debug

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 26s
38 actionable tasks: 10 executed, 28 up-to-date
