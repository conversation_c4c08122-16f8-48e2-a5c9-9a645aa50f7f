 Task :app:compileDebugKotlin FAILED
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:24:31 Unresolved reference 'theme'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:58:38 Unresolved reference 'SurfaceVariantDark'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:72:33 Unresolved reference 'TextPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:84:41 Unresolved reference 'TextSecondary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:102:50 Unresolved reference 'AccentPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:103:52 Unresolved reference 'DividerColor'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:104:49 Unresolved reference 'AccentPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:105:51 Unresolved reference 'TextSecondary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:106:48 Unresolved reference 'TextPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:107:50 Unresolved reference 'TextPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:108:43 Unresolved reference 'AccentPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:127:48 Unresolved reference 'TextSecondary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:130:41 Null cannot be a value of a non-null type 'androidx.compose.ui.graphics.Brush'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:154:50 Unresolved reference 'AccentPrimary'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/components/NumericalInputDialog.kt:155:48 Unresolved reference 'BackgroundDark'.

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 33s
29 actionable tasks: 6 executed, 23 up-to-date